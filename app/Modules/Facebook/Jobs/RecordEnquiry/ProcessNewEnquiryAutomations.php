<?php

declare(strict_types=1);

namespace App\Modules\Facebook\Jobs\RecordEnquiry;

use App\AutomationRule;
use App\BackendModel\Enquiry;
use App\BackendModel\WhatsappTemplate;
use App\Common\Common;
use App\Common\Variables;
use App\FeatureRelease\Facade\FeatureReleaseChecker;
use App\FeatureRelease\TargetContext;
use App\Ivr\IVRWebhook\Jobs\AutomationApiCall;
use App\Modules\Facebook\Jobs\RecordEnquiry\Automations\AutomationProcessor;
use App\User;
use Getlead\Campaign\Models\LeadCampaign;
use Getlead\Messagebird\Common\GupShup;
use Getlead\Messagebird\Models\WatsappCredential;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;

class ProcessNewEnquiryAutomations
{
    public function __construct(
        private readonly GupShup $gupshup,
        private readonly Common $common,
        private readonly CutisInternationalCustomAutomation $cutisInternationalCustomAutomation,
        private readonly AutomationProcessor $automationProcessor,
    ) {
    }

    public function for(Enquiry $enquiry): void
    {
        $vendorId = $enquiry->fk_int_user_id;
        $sourceId = $enquiry->fk_int_enquiry_type_id;

        Log::withContext([
            'vendor_id' => $vendorId,
            'source_id' => $sourceId,
        ]);

        $this->cutisInternationalCustomAutomation->for(enquiry: $enquiry);

        $automationRules = $this->getAutomationRules(vendorId: $vendorId, sourceId: $sourceId);

        if ($automationRules->isEmpty()) {
            Log::info('No rules found for enquiry');
            return;
        }

        $this->processWhatsappAutomation(
            automationRules: $automationRules,
            sourceId: $sourceId,
            vendorId: $vendorId,
            enquiry: $enquiry
        );

        $this->processApiAutomation(automationRules: $automationRules, vendorId: $vendorId, enquiry: $enquiry);

        $this->processCampaignAutomation(
            automationRules: $automationRules,
            vendorId: $vendorId,
            sourceId: $sourceId,
            enquiry: $enquiry
        );

        $this->automationProcessor
            ->process(enquiry: $enquiry, rules: $automationRules);
    }

    private function getAutomationRules(int $vendorId, int $sourceId): Collection
    {
        return AutomationRule::query()
            ->where('vendor_id', '=', $vendorId)
            ->where(static function (Builder $builder) use ($sourceId): void {
                $builder->where('enquiry_source_id', '=', $sourceId)
                    ->orWhereNull('enquiry_source_id');
            })
            ->orderBy('id', 'DESC')
            ->get();
    }

    private function processWhatsappAutomation(
        Collection $automationRules,
        int $sourceId,
        int $vendorId,
        Enquiry $enquiry
    ): void {
        $whatsappAutomation = $automationRules->where('action', 'whatsapp')
            ->where('trigger', 'new_lead')
            ->where('enquiry_source_id', $sourceId)
            ->first();

        if (! $whatsappAutomation instanceof AutomationRule) {
            Log::info('Whatsapp automation rule not found');
            return;
        }

        $template = WhatsappTemplate::query()
            ->where('pk_int_whatsapp_template_id', '=', $whatsappAutomation->whatsapp_template_id)
            ->first('text_whatsapp_template_description');

        if (! $template instanceof WhatsappTemplate) {
            Log::error('Whatsapp template not found');
            return;
        }

        $this->sendWhatsappMessage(
            vendorId: $vendorId,
            customerMobileNumber: $enquiry->vchr_customer_mobile,
            message: str_replace(
                '{{name}}',
                $enquiry->vchr_customer_name,
                $template->text_whatsapp_template_description
            ),
        );
    }

    /**
     * @note Country id doesn't seem to exist in the enquiry model leaving it as optional
     */
    private function sendWhatsappMessage(
        int $vendorId,
        string $customerMobileNumber,
        string $message,
        ?string $countryId = ''
    ): void {
        $credential = WatsappCredential::query()
            ->where('vendor_id', '=', $vendorId)
            ->where('status', '=', true)
            ->where('platform_id', '=', 2)
            ->first();

        if (! $credential instanceof WatsappCredential) {
            Log::error('Whatsapp credential not found');
            return;
        }

        Log::info('Whatsapp message sending attempt');

        $this->gupshup->sendWatsappMessageIndividal($countryId, $customerMobileNumber, $message, [
            'api_key' => $credential->access_key,
            'from_number' => $credential->source_mobile_num,
            'app_name' => $credential->template_name,
        ]);
    }

    private function processApiAutomation(Collection $automationRules, int $vendorId, Enquiry $enquiry): void
    {
        if ($vendorId !== Variables::NIKSHAN_USER_ID) {
            Log::info('Nikshan automation not implemented');
            return;
        }

        $apiAutomation = $automationRules->where('action', 'api')
            ->where('trigger', 'new_lead')
            ->first();

        if (! $apiAutomation instanceof AutomationRule || $apiAutomation->api === null) {
            Log::error('Api automation rule not found');
            return;

        }

        if (FeatureReleaseChecker::isEnabled('new-lead-automation-api-call', new TargetContext($vendorId))) {
            Bus::dispatch(new AutomationApiCall(
                enquiryId: $enquiry->pk_int_enquiry_id,
                vendorId: $vendorId,
                automationId: $apiAutomation->id,
            ));
            return;
        }

        $user = $enquiry->assigned_user;

        if (! $user instanceof User) {
            Log::error('User not found');
            return;
        }

        $this->common->postToIvrAutoCall(
            url: $apiAutomation->api,
            extension: $user->userExtension?->extension,
            data: [
                'phone' => $enquiry->vchr_customer_mobile,
            ]
        );
    }

    private function processCampaignAutomation(
        Collection $automationRules,
        int $vendorId,
        int $sourceId,
        Enquiry $enquiry
    ): void {
        $campaignAutomation = $automationRules->where('action', 'add_to_campaign')
            ->where('enquiry_source_id', $sourceId)
            ->where('trigger', 'new_lead')
            ->first();

        if (! $campaignAutomation instanceof AutomationRule || $campaignAutomation->campaign_id === null) {
            Log::error('Campaign automation rule not found');
            return;
        }

        $campaign = LeadCampaign::query()
            ->where('id', '=', $campaignAutomation->campaign_id)
            ->first(['id', 'type']);

        if (! $campaign instanceof LeadCampaign) {
            Log::error('Campaign not found');
            return;
        }

        $this->common->addToCampaign(
            automation: $campaignAutomation,
            lead: $enquiry,
            userId: $vendorId,
            vendorId: $vendorId,
            type: $campaign->type
        );
    }
}
