<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('automation_triggers')->insert([
            'value' => 'purpose_change',
            'title' => 'Purpose Change',
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('automation_triggers')->where('value', 'purpose_change')->delete();
    }
};
